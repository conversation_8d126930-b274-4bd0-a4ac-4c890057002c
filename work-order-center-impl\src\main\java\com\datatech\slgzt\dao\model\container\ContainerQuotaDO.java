package com.datatech.slgzt.dao.model.container;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 容器配额表
 * <AUTHOR>
 * @description 容器配额数据对象
 * @date 2025年05月27日
 */
@Data
@TableName("WOC_CONTAINER_QUOTA")
public class ContainerQuotaDO {

    /**
     * 主键ID
     */
    @TableField("ID")
    private Long id;

    /**
     * 工单ID
     */
    @TableField("WORK_ORDER_ID")
    private String workOrderId;

    /**
     * 子订单ID
     */
    @TableField("SUB_ORDER_ID")
    private String subOrderId;

    /**
     * 业务系统ID
     */
    @TableField("BUSINESS_SYSTEM_ID")
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    @TableField("BUSINESS_SYSTEM_NAME")
    private String businessSystemName;

    /**
     * 配额名称
     */
    @TableField("CQ_NAME")
    private String cqName;

    /**
     * 容器配额-核心数
     */
    @TableField("V_CPUS")
    private Integer vCpus;

    /**
     * 容器配额-内存大小，单位G
     */
    @TableField("RAM")
    private Integer ram;

    /**
     * GPU算力
     */
    @TableField("GPU_RATIO")
    private Integer gpuRatio;

    /**
     * GPU显存大小，单位GB
     */
    @TableField("GPU_VIRTUAL_MEMORY")
    private Integer gpuVirtualMemory;

    /**
     * 物理GPU卡(个)
     */
    @TableField("GPU_CORE")
    private Integer gpuCore;

    /**
     * 虚拟GPU卡(个)
     */
    @TableField("GPU_VIRTUAL_CORE")
    private Integer gpuVirtualCore;

    /**
     * 4A账号
     */
    @TableField("A4_ACCOUNT")
    private String a4Account;

    /**
     * 4A账号绑定的手机
     */
    @TableField("A4_PHONE")
    private String a4Phone;

    /**
     * 申请时长
     */
    @TableField("APPLY_TIME")
    private String applyTime;

    /**
     * 开通数量
     */
    @TableField("OPEN_NUM")
    private Integer openNum;

    /**
     * 云类型编码
     */
    @TableField("CATALOGUE_DOMAIN_CODE")
    private String catalogueDomainCode;

    /**
     * 云类型名称
     */
    @TableField("CATALOGUE_DOMAIN_NAME")
    private String catalogueDomainName;

    /**
     * 云平台编码
     */
    @TableField("DOMAIN_CODE")
    private String domainCode;

    /**
     * 云平台名称
     */
    @TableField("DOMAIN_NAME")
    private String domainName;

    /**
     * 资源池ID
     */
    @TableField("REGION_ID")
    private Long regionId;

    /**
     * 资源池编码
     */
    @TableField("REGION_CODE")
    private String regionCode;

    /**
     * 资源池名称
     */
    @TableField("REGION_NAME")
    private String regionName;

    /**
     * 状态
     */
    @TableField("STATUS")
    private String status;

    /**
     * 原始名称
     */
    @TableField("ORIGIN_NAME")
    private String originName;

    /**
     * 申请用户ID
     */
    @TableField("APPLY_USER_ID")
    private Long applyUserId;

    /**
     * 申请用户名称
     */
    @TableField("APPLY_USER_NAME")
    private String applyUserName;

    /**
     * 是否启用
     */
    @TableLogic(value = "1", delval = "0")
    @TableField("ENABLED")
    private Boolean enabled;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("MODIFY_TIME")
    private LocalDateTime modifyTime;

    /**
     * 所属部门名称
     */
    @TableField("DEPARTMENT_NAME")
    private String departmentName;


    /**
     * 租户ID
     */
    @TableField("TENANT_ID")
    private Long tenantId;

    /**
     * 租户名称
     */
    @TableField("TENANT_NAME")
    private String tenantName;


    //回收状态
    @TableField("RECOVERY_STATUS")
    private Integer recoveryStatus;

    /**
     * NPU资源数量
     */
    @TableField("NPU")
    private Integer npu;

    /**
     * 类型，0-普通容器，1-NPU容器
     */
    @TableField("TYPE")
    private Integer type;
}
