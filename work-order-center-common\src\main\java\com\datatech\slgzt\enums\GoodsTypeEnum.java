package com.datatech.slgzt.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 商品类型枚举
 *
 * <AUTHOR>
 */

@Getter
public enum GoodsTypeEnum {
    /**
     * 云产品类型
     */
    ECS("ecs", "云主机", "ECS"),
    EVS("evs", "云硬盘", "EVS"),
    SYS_EVS("sysEvs", "系统云硬盘", "SYS_EVS"),
    SHARE_EVS("shareEvs", "共享云硬盘", "SHARE_EVS"),
    EIP("eip", "弹性IP","EIP"),
    MYSQL("mysql", "云数据库", "MYSQL"),
    POSTGRESQL("postgreSql", "postgre云数据库", "POSTGRESQL"),
    OTHER("other", "其他产品", "OTHER"),
    GCS("gcs", "GPU云主机", "GCS"),
    REDIS("redis", "redis", "REDIS"),
    OBS("obs", "对象存储", "OBS"),
    SLB("slb", "负载均衡", "SLB"),
    NAT("nat", "NAT网关", "NAT"),
    VPC("vpc", "VPC", "VPC"),
    BACKUP("backup", "BACKUP", "BACKUP"),
    VPN("vpn", "VPN", "VPN"),
    NAS("nas", "NAS", "NAS"),
    BASE("base", "订单中基础信息", "BASE"),
    NETWORK("network", "网络", "NETWORK"),
    NETCARD("netcard", "虚拟网卡", "NET_CARD"),
    PHYSICAL_MACHINE("pm", "物理机", "PM"),
    FLINK("flink", "flink", "FLINK"),
    KAFKA("kafka", "kafka", "KAFKA"),
    ES("es", "es", "ES"),
    RDS_MYSQL("rdsMysql", "云数据库","RdsMysql"),
    BLD_REDIS("bldRedis", "宝兰德redis","BldRedis"),
    NPU_PHYSICAL_MACHINE("npm", "NPU裸金属", "NPM"),
    UNKNOWN("unknown", "-", null),
    ;

    private final String code;
    private final String desc;
    private final String alias;

    GoodsTypeEnum(String code, String desc, String alias) {
        this.code = code;
        this.desc = desc;
        this.alias = alias;
    }

    /**
     * 通过code获取enum
     */
    public static GoodsTypeEnum getByCode(String code) {
        if (!StringUtils.isEmpty(code)) {
            for (GoodsTypeEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
        }
        return GoodsTypeEnum.UNKNOWN;
    }
}