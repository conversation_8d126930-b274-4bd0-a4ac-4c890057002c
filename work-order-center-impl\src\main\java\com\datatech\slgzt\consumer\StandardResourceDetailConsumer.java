package com.datatech.slgzt.consumer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.convert.StandardResourceDetailConsumerConvert;
import com.datatech.slgzt.dao.mapper.ObsOpenTaskMapper;
import com.datatech.slgzt.dao.mapper.VmMapper;
import com.datatech.slgzt.dao.mapper.network.NetworkIpAddressMapper;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.enums.ip.IPVersionEnum;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.query.StandardWorkOrderProductQuery;
import com.datatech.slgzt.service.external.ExternalResourceDetailService;
import com.datatech.slgzt.utils.DateUtils;
import com.datatech.slgzt.utils.IpUtils;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.StreamUtils;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月12日 15:20:59
 */
@Slf4j
@Service
public class StandardResourceDetailConsumer {


    @Resource
    private StandardWorkOrderManager standardWorkOrderManager;

    @Resource
    private StaticProductStockManager staticProductStockManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private VmMapper vmMapper;

    @Resource
    private VpcOrderManager vpcOrderManager;

    @Resource
    private CpcProjectManager cpcProjectManager;

    @Resource
    private StandardWorkOrderProductManager productManager;

    @Resource
    private ObsOpenTaskMapper obsOpenTaskMapper;
    @Resource
    private ExternalResourceDetailService externalResourceDetailService;
    @Resource
    private NetworkIpAddressMapper networkIpAddressMapper;

    @Resource
    private StandardResourceDetailConsumerConvert converter;

    @Resource
    private VnicManager vnicManager;

    @Resource
    private FlavorModelManager flavorManager;

    public void consumeResourceMessage(List<ResourceDetailDTO> list, List<ResourceDetailDTO> externalList) {
        if (CollectionUtil.isNotEmpty(externalList)) {
            externalResourceDetailService.consumeResourceMessage(externalList);
        }
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        //处理集合封装资源详情
        Map<Long, ResourceDetailDTO> gooodId2IdentityMap = StreamUtils.toMap(list, ResourceDetailDTO::getGoodsOrderId);
        List<ResourceDetailDTO> needInsertList = new ArrayList<>();
        log.info("监听任务消息: 标准工单对象列表={}", JSON.toJSON(gooodId2IdentityMap));
        List<StandardWorkOrderProductDTO> productDTOS = productManager.list(new StandardWorkOrderProductQuery().setSubOrderIds(gooodId2IdentityMap.keySet()));
        if (CollectionUtil.isNotEmpty(productDTOS)) {
            productDTOS.forEach(productDTO -> {
                //去detail表中找到对应的数据 如果已经存在则不处理
                if (resourceDetailManager.getByGoodsOrderId(productDTO.getSubOrderId()) != null) {
                    log.info("kafka资源开通回调，待更新detail表数据已存在：产品表的 subOrderId={}", productDTO.getSubOrderId());
                    //暂时做个补偿更新
                    obsOpenTaskMapper.updateByProductOrderId(ResOpenEnum.OPEN_SUCCESS.getCode(), productDTO.getId());
                    return;
                }
                ResourceDetailDTO detail = gooodId2IdentityMap.get(productDTO.getSubOrderId());
                StandardWorkOrderDTO orderDTO = standardWorkOrderManager.getById(productDTO.getWorkOrderId());
                if (Objects.nonNull(orderDTO)) {
                    detail.setType(productDTO.getProductType());
                    detail.setOrderId(orderDTO.getId());
                    detail.setOrderCode(orderDTO.getOrderCode());
                    detail.setTenantId(orderDTO.getTenantId());
                    detail.setTenantName(orderDTO.getTenantName());
                    detail.setBusinessSysName(orderDTO.getBusinessSystemName());
                    detail.setApplyUserId(orderDTO.getCreatedBy());
                    detail.setApplyUserName(orderDTO.getCreatedUserName());
                    detail.setCreateTime(LocalDateTime.now());
                    detail.setStatus(1);
                    detail.setBillId(orderDTO.getBillId());
                    detail.setCloudPlatform(orderDTO.getDomainName());
                    detail.setBusinessSysId(orderDTO.getBusiSystemId());
                    detail.setDomainCode(orderDTO.getDomainCode());
                    detail.setDomainName(orderDTO.getDomainName());
                    detail.setModuleId(orderDTO.getModuleId());
                    detail.setModuleName(orderDTO.getModuleName());
                }
                switch (ProductTypeEnum.getByCode(detail.getType())) {
                    case ECS:
                    case GCS:
                    case MYSQL:
                    case POSTGRESQL:
                    case REDIS:
                        coverEcs(detail, productDTO, needInsertList);
                        break;
                    case EIP:
                        coverEip(detail, productDTO);
                        break;
                    case EVS:
                        convertEvs(detail, productDTO);
                        break;
                    case SHARE_EVS:
                        convertShareEvs(detail, productDTO);
                        break;
                    case OBS:
                        coverObs(detail, productDTO);
                        //加个补充逻辑，更新工单为开通成功
                        productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPEN_SUCCESS.getCode());
                        productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPEN_SUCCESS.getCode());
                        obsOpenTaskMapper.updateByProductOrderId(ResOpenEnum.OPEN_SUCCESS.getCode(), productDTO.getId());
                        break;
                    case SLB:
                        coverSlb(detail, productDTO, needInsertList);
                        break;
                    case NAT:
                        coverNat(detail, productDTO, needInsertList);
                        break;
                    case VPN:
                        convertVpn(detail, productDTO);
                        break;
                    case NPU_PHYSICAL_MACHINE:
                        convertNpu(detail, productDTO, needInsertList);
                        break;
                    default:
                }
                needInsertList.add(detail);

            });
        }

        log.info("kafka资源开通回调，待更新detail表数据：{}", list);
        needInsertList.forEach(o -> {
            try {
                resourceDetailManager.saveResourceDetail(o);
            } catch (Exception e) {
                //这里最好入库对象 做补偿
                log.error("kafka资源开通回调，待更新detail表数据异常：{}", JSON.toJSONString(o), e);
            }
        });
    }

    public void coverEcs(ResourceDetailDTO detail, StandardWorkOrderProductDTO productDTO, List<ResourceDetailDTO> needInsertList) {
        String propertySnapshot = productDTO.getPropertySnapshot();
        EcsModel escModel = JSON.parseObject(propertySnapshot, EcsModel.class);
        updateNetwork(escModel.getPlaneNetworkModel(), detail.getDeviceId());
        List<PlaneNetworkModel> planeNetworkModel = escModel.getPlaneNetworkModel();
        log.debug("coverEcs list:{}", JSONObject.toJSONString(planeNetworkModel));
        //如果domainCode创新池查询项目名称
        String domainCode = detail.getDomainCode();
        if (CatalogueDomain.INNOVATION.getCode().equals(domainCode)) {
            String projectId = vmMapper.getByDeviceId(detail.getDeviceId());
            //获取项目名称
            String projectName = cpcProjectManager.getById(projectId);
            //detail中已知任务中心会传值过来的字段,goodsOrderId,deviceName,eip,bandWidth,spec,deviceId,resourcePoolId,resourcePoolName,deviceStatus,resourceApplyTime
            detail.setProjectName(projectName);
        }
        // 找主网卡(目前是只有主网卡支持才支持)
        PlaneNetworkModel mainPlaneNetworkModel = planeNetworkModel.get(0);
        if ("vpc".equals(mainPlaneNetworkModel.getType()) && !mainPlaneNetworkModel.getId().startsWith("net")) {
            // 根据id查询vpc
            VpcOrderExtDTO vpcOrder = vpcOrderManager.getById(mainPlaneNetworkModel.getId());
            //
            detail.setIpv6Enable(vpcOrder.getIpv6Enable());
        }
        detail.setVpcId(Joiner.on(",")
                              .skipNulls()
                              .join(StreamUtils.mapArrayFilterNull(planeNetworkModel, PlaneNetworkModel::getId)));
        detail.setVpcName(Joiner.on(",")
                                .skipNulls()
                                .join(StreamUtils.mapArrayFilterNull(planeNetworkModel, PlaneNetworkModel::getName)));
        //子网可能出现一个以上的情况，用|分隔 每个网络模型的子网id用逗号分隔
        String subnetId = planeNetworkModel.stream()
                                           .map(networkModel ->
                                                   Joiner.on(",").skipNulls().join(
                                                           StreamUtils.mapArrayFilterNull(networkModel.getSubnets(), PlaneNetworkModel.Subnet::getSubnetId)
                                                   )
                                           )
                                           .filter(s -> !s.isEmpty())  // 过滤掉空字符串
                                           .collect(Collectors.joining("|"));
        String subnetName = planeNetworkModel.stream()
                                             .map(networkModel ->
                                                     Joiner.on(",").skipNulls().join(
                                                             StreamUtils.mapArrayFilterNull(networkModel.getSubnets(), PlaneNetworkModel.Subnet::getSubnetName)
                                                     )
                                             )
                                             .filter(s -> !s.isEmpty())  // 过滤掉空字符串
                                             .collect(Collectors.joining("|"));
        detail.setSubnetId(subnetId);
        detail.setSubnetName(subnetName);
        detail.setCloudPlatform(escModel.getDomainCode());
        //mysql类型的时候会出现，表示主从类型
        detail.setMountOrNot(escModel.getDeployType());
        detail.setApplyTime(escModel.getApplyTime());//已转换成one_month
        detail.setResourcePoolId(escModel.getRegionId().toString());
        detail.setResourcePoolCode(escModel.getRegionCode());
        detail.setResourcePoolName(escModel.getRegionName());
        detail.setEffectiveTime(LocalDateTime.now());
        detail.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), escModel.getApplyTime()));
        detail.setNetworkModelSnapshot(JSON.toJSONString(escModel.getPlaneNetworkModel()));
        //修改下带宽适配问题
        detail.setBandWidth(bandWidth(detail.getBandWidth()));
        //evs
        if (StringUtils.isNotBlank(detail.getVolumeId())) {
            List<String> list = Arrays.asList(detail.getVolumeId().split(","));
            for (int i = 0; i < list.size(); i++) {
                ResourceDetailDTO detailDTO = getResourceDetailDTO(detail, escModel);
                detailDTO.setType(ProductTypeEnum.EVS.getCode());
                detailDTO.setGoodsOrderId(IdUtil.getSnowflake().nextId());
                detailDTO.setDeviceId(list.get(i));
                detailDTO.setVolumeId(list.get(i));
                detailDTO.setDataDisk(detail.getDataDisk().split(",")[i]);
                detailDTO.setDeviceStatus("USED");
                detailDTO.setVmId(detail.getDeviceId());
                detailDTO.setEcsName(detail.getDeviceName());
                needInsertList.add(detailDTO);
            }
        }
        //eip
        if (StringUtils.isNotBlank(detail.getEipId())) {
//            List<String> list = Arrays.asList(detail.getEipId().split(","));
//            List<String> eipList = Arrays.asList(detail.getEip().split(","));
//            for (int i = 0; i < eipList.size(); i++) {
//                ResourceDetailDTO detailDTO = getResourceDetailDTO(detail, escModel);
//                detailDTO.setDeviceName(detail.getDeviceName() + "-eip");
//                detailDTO.setRelatedDeviceId(detail.getDeviceId());
//                detailDTO.setRelatedDeviceName(detail.getDeviceName());
//                detailDTO.setRelatedDeviceType(detail.getType());
//                detailDTO.setType(ProductTypeEnum.EIP.getCode());
//                detailDTO.setGoodsOrderId(IdUtil.getSnowflake().nextId());
//                detailDTO.setDeviceId(list.get(i));
//                detailDTO.setEipId(list.get(i));
//                String eip = eipList.get(i);
//                detailDTO.setEip(eip);
//                detailDTO.setBandWidth(detail.getBandWidth());
//                detailDTO.setDeviceStatus("ACTIVE");
//                detailDTO.setVmId(detail.getDeviceId());
//                detailDTO.setEcsName(detail.getDeviceName());
//                detailDTO.setIpVersion(IpUtils.isIPv4(eip) ? IPVersionEnum.IPV4.getDesc() : IPVersionEnum.IPV6.getDesc());
//                needInsertList.add(detailDTO);
//            }
            //调整一下逻辑，直接查询ipAddress表
            String deviceId = detail.getDeviceId();
            List<IpAddress> ipAddressList = vmMapper.getIpAddress(deviceId, "ECS");
            ipAddressList.removeIf(ip -> !"EIP".equals(ip.getType()));
            if (CollectionUtil.isNotEmpty(ipAddressList)) {
                for (IpAddress ipAddress : ipAddressList) {
                    ResourceDetailDTO detailDTO = getResourceDetailDTO(detail, escModel);
                    if (ipAddressList.size() > 1) {
                        detailDTO.setDeviceName(detail.getDeviceName() + "-eip-" + (IpUtils.isIPv4(ipAddress.getIp()) ? IPVersionEnum.IPV4.getDesc() : IPVersionEnum.IPV6.getDesc()));
                    } else {
                        detailDTO.setDeviceName(detail.getDeviceName() + "-eip");
                    }
                    detailDTO.setRelatedDeviceId(detail.getDeviceId());
                    detailDTO.setRelatedDeviceName(detail.getDeviceName());
                    detailDTO.setRelatedDeviceType(detail.getType());
                    detailDTO.setType(ProductTypeEnum.EIP.getCode());
                    detailDTO.setGoodsOrderId(IdUtil.getSnowflake().nextId());
                    detailDTO.setDeviceId(ipAddress.getId());
                    detailDTO.setEipId(ipAddress.getId());
                    detailDTO.setEip(ipAddress.getIp());
                    detailDTO.setBandWidth(detail.getBandWidth());
                    detailDTO.setDeviceStatus("ACTIVE");
                    detailDTO.setVmId(detail.getDeviceId());
                    detailDTO.setEcsName(detail.getDeviceName());
                    detailDTO.setIpVersion(IpUtils.isIPv4(ipAddress.getIp()) ? IPVersionEnum.IPV4.getDesc() : IPVersionEnum.IPV6.getDesc());
                    needInsertList.add(detailDTO);
                }
            }
        }
        //主网卡信息入库
        List<IpAddress> ipAddresses = vmMapper.getIpAddresses(detail.getDeviceId(), "ECS");
        ipAddresses = ipAddresses.stream().filter(ip -> !"EIP".equals(ip.getType())).collect(Collectors.toList());
        //如果是创新池，则根据portId分组入库（多网卡ipv4，ipv6）
        if (CatalogueDomain.INNOVATION.getCode().equals(domainCode)) {
            Map<String, List<IpAddress>> collect = ipAddresses.stream().collect(Collectors.groupingBy(IpAddress::getPortId));
            for (String key : collect.keySet()) {
                List<IpAddress> ipList = collect.get(key);
                IpAddress entity = ipList.get(0);
                VnicDTO vnicDTO = new VnicDTO();
                vnicDTO.setId(IdUtil.getSnowflake().nextIdStr());
                vnicDTO.setVnicId(key);
                vnicDTO.setVnicName(entity.getName());
                vnicDTO.setBusinessSystemName(detail.getBusinessSysName());
                vnicDTO.setBusinessSystemId(String.valueOf(detail.getBusinessSysId()));
                vnicDTO.setDomainName(detail.getDomainName());
                vnicDTO.setDomainCode(domainCode);
                CatalogueDomain catalogueDomain = CatalogueDomain.getByCode(domainCode);
                vnicDTO.setCatalogueDomainCode(catalogueDomain.getParent().getCode());
                vnicDTO.setCatalogueDomainName(catalogueDomain.getParent().getName());
                vnicDTO.setRegionId(detail.getResourcePoolId());
                vnicDTO.setRegionName(detail.getResourcePoolName());
                vnicDTO.setRegionCode(detail.getResourcePoolCode());
                vnicDTO.setAzId(detail.getAzId());
                vnicDTO.setAzName(detail.getAzName());
                vnicDTO.setAzCode(detail.getAzCode());
                vnicDTO.setVpcId(detail.getVpcId());
                vnicDTO.setVpcName(detail.getVpcName());
                vnicDTO.setVmName(detail.getDeviceName());
                vnicDTO.setVmId(detail.getDeviceId());
                vnicDTO.setTenantId(detail.getTenantId());
                vnicDTO.setTenantName(detail.getTenantName());
                if ("BUSINESS".equals(entity.getType())) {
                    vnicDTO.setType("2");
                } else if ("MANAGE".equals(entity.getType())) {
                    vnicDTO.setType("3");
                }
                ipList.forEach(ipAddress -> {
                    String ip = ipAddress.getIp();
                    if (IpUtils.isIPv4(ip)) {
                        vnicDTO.setSubnetId(ipAddress.getSubnetId());
                        vnicDTO.setSubnetName(ipAddress.getSubnetName());
                        vnicDTO.setIpAddress(ip);
                    } else if (IpUtils.isIPv6(ip)) {
                        vnicDTO.setIpv6SubnetId(ipAddress.getSubnetId());
                        vnicDTO.setIpv6SubnetName(ipAddress.getSubnetName());
                        vnicDTO.setIpV6IpAddress(ip);
                    }
                });
                vnicManager.save(vnicDTO);
            }
        } else {
            for (IpAddress ipAddress : ipAddresses) {
                VnicDTO vnicDTO = new VnicDTO();
                vnicDTO.setId(IdUtil.getSnowflake().nextIdStr());
                vnicDTO.setVnicId(ipAddress.getId());
                vnicDTO.setVnicName(ipAddress.getName());
                vnicDTO.setBusinessSystemName(detail.getBusinessSysName());
                vnicDTO.setBusinessSystemId(String.valueOf(detail.getBusinessSysId()));
                vnicDTO.setDomainName(detail.getDomainName());
                vnicDTO.setDomainCode(domainCode);
                CatalogueDomain catalogueDomain = CatalogueDomain.getByCode(domainCode);
                vnicDTO.setCatalogueDomainCode(catalogueDomain.getParent().getCode());
                vnicDTO.setCatalogueDomainName(catalogueDomain.getParent().getName());
                vnicDTO.setRegionId(detail.getResourcePoolId());
                vnicDTO.setRegionName(detail.getResourcePoolName());
                vnicDTO.setRegionCode(detail.getResourcePoolCode());
                vnicDTO.setAzId(detail.getAzId());
                vnicDTO.setAzName(detail.getAzName());
                vnicDTO.setAzCode(detail.getAzCode());
                vnicDTO.setVpcId(detail.getVpcId());
                vnicDTO.setVpcName(detail.getVpcName());
                vnicDTO.setSubnetId(detail.getSubnetId());
                vnicDTO.setSubnetName(detail.getSubnetName());
                vnicDTO.setIpAddress(ipAddress.getIp());
                vnicDTO.setVmName(detail.getDeviceName());
                vnicDTO.setVmId(detail.getDeviceId());
                vnicDTO.setTenantId(detail.getTenantId());
                vnicDTO.setTenantName(detail.getTenantName());
                if ("BUSINESS".equals(ipAddress.getType())) {
                    vnicDTO.setType("2");
                } else if ("MANAGE".equals(ipAddress.getType())) {
                    vnicDTO.setType("3");
                }
                vnicManager.save(vnicDTO);
            }
        }
    }

    public void convertNpu(ResourceDetailDTO detail, StandardWorkOrderProductDTO productDTO, List<ResourceDetailDTO> needInsertList) {
        String propertySnapshot = productDTO.getPropertySnapshot();
        NpuPhysicalMachineModel npuModel = JSON.parseObject(propertySnapshot, NpuPhysicalMachineModel.class);
        updateNetwork(npuModel.getPlaneNetworkModel(), detail.getDeviceId());
        List<PlaneNetworkModel> planeNetworkModel = npuModel.getPlaneNetworkModel();
        log.debug("convertNpu list:{}", JSONObject.toJSONString(planeNetworkModel));
        //如果domainCode创新池查询项目名称
        String domainCode = detail.getDomainCode();
        // 找主网卡(目前是只有主网卡支持才支持)
        PlaneNetworkModel mainPlaneNetworkModel = planeNetworkModel.get(0);
        if ("vpc".equals(mainPlaneNetworkModel.getType()) && !mainPlaneNetworkModel.getId().startsWith("net")) {
            // 根据id查询vpc
            VpcOrderExtDTO vpcOrder = vpcOrderManager.getById(mainPlaneNetworkModel.getId());
            //
            detail.setIpv6Enable(vpcOrder.getIpv6Enable());
        }
        detail.setVpcId(Joiner.on(",")
                .skipNulls()
                .join(StreamUtils.mapArrayFilterNull(planeNetworkModel, PlaneNetworkModel::getId)));
        detail.setVpcName(Joiner.on(",")
                .skipNulls()
                .join(StreamUtils.mapArrayFilterNull(planeNetworkModel, PlaneNetworkModel::getName)));
        //子网可能出现一个以上的情况，用|分隔 每个网络模型的子网id用逗号分隔
        String subnetId = planeNetworkModel.stream()
                .map(networkModel ->
                        Joiner.on(",").skipNulls().join(
                                StreamUtils.mapArrayFilterNull(networkModel.getSubnets(), PlaneNetworkModel.Subnet::getSubnetId)
                        )
                )
                .filter(s -> !s.isEmpty())  // 过滤掉空字符串
                .collect(Collectors.joining("|"));
        String subnetName = planeNetworkModel.stream()
                .map(networkModel ->
                        Joiner.on(",").skipNulls().join(
                                StreamUtils.mapArrayFilterNull(networkModel.getSubnets(), PlaneNetworkModel.Subnet::getSubnetName)
                        )
                )
                .filter(s -> !s.isEmpty())  // 过滤掉空字符串
                .collect(Collectors.joining("|"));
        detail.setSubnetId(subnetId);
        detail.setSubnetName(subnetName);
        detail.setCloudPlatform(npuModel.getDomainCode());
        //mysql类型的时候会出现，表示主从类型
        detail.setApplyTime(npuModel.getApplyTime());//已转换成one_month
        detail.setResourcePoolId(npuModel.getRegionId().toString());
        detail.setResourcePoolCode(npuModel.getRegionCode());
        detail.setResourcePoolName(npuModel.getRegionName());
        detail.setEffectiveTime(LocalDateTime.now());
        detail.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), npuModel.getApplyTime()));
        detail.setNetworkModelSnapshot(JSON.toJSONString(npuModel.getPlaneNetworkModel()));
        //修改下带宽适配问题
        detail.setBandWidth(bandWidth(detail.getBandWidth()));

        convertNpuFlaver(detail, npuModel);
        //eip
        if (StringUtils.isNotBlank(detail.getEipId())) {
//            List<String> list = Arrays.asList(detail.getEipId().split(","));
//            List<String> eipList = Arrays.asList(detail.getEip().split(","));
//            for (int i = 0; i < eipList.size(); i++) {
//                ResourceDetailDTO detailDTO = getResourceDetailDTO(detail, npuModel);
//                detailDTO.setDeviceName(detail.getDeviceName() + "-eip");
//                detailDTO.setRelatedDeviceId(detail.getDeviceId());
//                detailDTO.setRelatedDeviceName(detail.getDeviceName());
//                detailDTO.setRelatedDeviceType(detail.getType());
//                detailDTO.setType(ProductTypeEnum.EIP.getCode());
//                detailDTO.setGoodsOrderId(IdUtil.getSnowflake().nextId());
//                detailDTO.setDeviceId(list.get(i));
//                detailDTO.setEipId(list.get(i));
//                String eip = eipList.get(i);
//                detailDTO.setEip(eip);
//                detailDTO.setBandWidth(detail.getBandWidth());
//                detailDTO.setDeviceStatus("ACTIVE");
//                detailDTO.setVmId(detail.getDeviceId());
//                detailDTO.setEcsName(detail.getDeviceName());
//                detailDTO.setIpVersion(IpUtils.isIPv4(eip) ? IPVersionEnum.IPV4.getDesc() : IPVersionEnum.IPV6.getDesc());
//                needInsertList.add(detailDTO);
//            }
            //调整一下逻辑，直接查询ipAddress表
            String deviceId = detail.getDeviceId();
            List<IpAddress> ipAddressList = vmMapper.getIpAddress(deviceId, "BARE_METAL");
            ipAddressList.removeIf(ip -> !"EIP".equals(ip.getType()));
            if (CollectionUtil.isNotEmpty(ipAddressList)) {
                for (IpAddress ipAddress : ipAddressList) {
                    ResourceDetailDTO detailDTO = getResourceDetailDTO(detail, npuModel);
                    if (ipAddressList.size() > 1) {
                        detailDTO.setDeviceName(detail.getDeviceName() + "-eip-" + (IpUtils.isIPv4(ipAddress.getIp()) ? IPVersionEnum.IPV4.getDesc() : IPVersionEnum.IPV6.getDesc()));
                    } else {
                        detailDTO.setDeviceName(detail.getDeviceName() + "-eip");
                    }
                    detailDTO.setRelatedDeviceId(detail.getDeviceId());
                    detailDTO.setRelatedDeviceName(detail.getDeviceName());
                    detailDTO.setRelatedDeviceType(detail.getType());
                    detailDTO.setType(ProductTypeEnum.EIP.getCode());
                    detailDTO.setGoodsOrderId(IdUtil.getSnowflake().nextId());
                    detailDTO.setDeviceId(ipAddress.getId());
                    detailDTO.setEipId(ipAddress.getId());
                    detailDTO.setEip(ipAddress.getIp());
                    detailDTO.setBandWidth(detail.getBandWidth());
                    detailDTO.setDeviceStatus("ACTIVE");
                    detailDTO.setVmId(detail.getDeviceId());
                    detailDTO.setEcsName(detail.getDeviceName());
                    detailDTO.setIpVersion(IpUtils.isIPv4(ipAddress.getIp()) ? IPVersionEnum.IPV4.getDesc() : IPVersionEnum.IPV6.getDesc());
                    needInsertList.add(detailDTO);
                }
            }
        }
        //主网卡信息入库
        List<IpAddress> ipAddresses = vmMapper.getIpAddresses(detail.getDeviceId(), "BARE_METAL");
        ipAddresses = ipAddresses.stream().filter(ip -> !"EIP".equals(ip.getType())).collect(Collectors.toList());
        for (IpAddress ipAddress : ipAddresses) {
            VnicDTO vnicDTO = new VnicDTO();
            vnicDTO.setId(IdUtil.getSnowflake().nextIdStr());
            vnicDTO.setVnicId(ipAddress.getId());
            vnicDTO.setVnicName(ipAddress.getName());
            vnicDTO.setBusinessSystemName(detail.getBusinessSysName());
            vnicDTO.setBusinessSystemId(String.valueOf(detail.getBusinessSysId()));
            vnicDTO.setDomainName(detail.getDomainName());
            vnicDTO.setDomainCode(domainCode);
            CatalogueDomain catalogueDomain = CatalogueDomain.getByCode(domainCode);
            vnicDTO.setCatalogueDomainCode(catalogueDomain.getParent().getCode());
            vnicDTO.setCatalogueDomainName(catalogueDomain.getParent().getName());
            vnicDTO.setRegionId(detail.getResourcePoolId());
            vnicDTO.setRegionName(detail.getResourcePoolName());
            vnicDTO.setRegionCode(detail.getResourcePoolCode());
            vnicDTO.setAzId(detail.getAzId());
            vnicDTO.setAzName(detail.getAzName());
            vnicDTO.setAzCode(detail.getAzCode());
            vnicDTO.setVpcId(detail.getVpcId());
            vnicDTO.setVpcName(detail.getVpcName());
            vnicDTO.setSubnetId(detail.getSubnetId());
            vnicDTO.setSubnetName(detail.getSubnetName());
            vnicDTO.setIpAddress(ipAddress.getIp());
            vnicDTO.setVmName(detail.getDeviceName());
            vnicDTO.setVmId(detail.getDeviceId());
            vnicDTO.setTenantId(detail.getTenantId());
            vnicDTO.setTenantName(detail.getTenantName());
            if ("BUSINESS".equals(ipAddress.getType())) {
                vnicDTO.setType("2");
            } else if ("MANAGE".equals(ipAddress.getType())) {
                vnicDTO.setType("3");
            }
            vnicManager.save(vnicDTO);
        }
    }

    private void convertNpuFlaver(ResourceDetailDTO detail, NpuPhysicalMachineModel npuModel) {
        // NAME             |DESCRIPTION|REGION_ID   |TENANT_ID|RAM    |DISK |VCPUS
        //-----------------+-----------+------------+---------+-------+-----+-----
        //640C2048GB/8*910C|           |100000002018|         |2097152|15842|  640
        FlavorDTO byFlavorId = flavorManager.getByFlavorId(npuModel.getFlavorId());

        //拆分成
        //GPU/NPU信息 8*910C
        //规格 640C2048GB
        //硬盘 15842GB
        //gpuCardType "NPU"
        //gpuNum 8
        //gpuType "910B4"
        //spec "192C2048GB"
        //storeType "192C2048GB"
        //dataDisk "15842GB"
        String[] flavorNames = npuModel.getFlavorName().split("/");
        String flaverName = flavorNames[0];
        String[] gpuInfo = flavorNames[1].split("\\*");
        detail.setGpuCardType("NPU");
        detail.setGpuType(gpuInfo[1]);
        detail.setGpuNum(Integer.parseInt(gpuInfo[0]));
        detail.setSpec(flaverName);
        detail.setStoreType(flaverName);
        detail.setDataDisk(byFlavorId.getDisk() + "GB");
    }

    private void updateNetwork(List<PlaneNetworkModel> list, String deviceId) {
        log.debug("updateNetwork deviceId:{},list:{}", deviceId, JSONObject.toJSONString(list));
        for (PlaneNetworkModel planeNetworkModel : list) {
            for (PlaneNetworkModel.Subnet subnet : planeNetworkModel.getSubnets()) {
                List<IpAddress> ipAddresses = networkIpAddressMapper.selectIpBySubnetId(deviceId, subnet.getSubnetId());
                for (IpAddress ipAddress : ipAddresses) {
                    if (ipAddress.getType().equals("MANAGE")) {
                        if (ipAddress.getIp().length() <= 18 && ipAddress.getIp().contains(".")) {
                            subnet.setManageIpv4(ipAddress.getIp());
                        } else {
                            subnet.setManageIpv6(ipAddress.getIp());
                        }
                    } else if (ipAddress.getType().equals("BUSINESS")) {
                        if (ipAddress.getIp().length() <= 18 && ipAddress.getIp().contains(".")) {
                            subnet.setBusinessIpv4(ipAddress.getIp());
                        } else {
                            subnet.setBusinessIpv6(ipAddress.getIp());
                        }
                    } else {
                        if (ipAddress.getIp().length() <= 18 && ipAddress.getIp().contains(".")) {
                            subnet.setAddressIpv4(ipAddress.getIp());
                        } else {
                            subnet.setAddressIpv6(ipAddress.getIp());
                        }
                    }
                }
            }
        }
        log.debug("updateNetwork end list:{}", JSONObject.toJSONString(list));
    }


    public void convertShareEvs(ResourceDetailDTO detail, StandardWorkOrderProductDTO productDTO) {
        if (detail.getEffectiveTime() == null && detail.getCreateTime() != null) {
            detail.setEffectiveTime(detail.getCreateTime());
        }
        String propertySnapshot = productDTO.getPropertySnapshot();
        EvsModel dataDiskModel = JSON.parseObject(propertySnapshot, EvsModel.class);
        if (StringUtils.isNotEmpty(dataDiskModel.getVmId())) {
            //挂载成功时,detail才会传值过来的字段ecsName
            if (StringUtils.isEmpty(detail.getEcsName())) {
                detail.setMountOrNot("挂载执行中");
            } else {
                detail.setMountOrNot("是");
            }
            //如果是挂载了云主机，更新对应的云主机挂盘id和数据盘容量
            ResourceDetailDTO ecsDetail = resourceDetailManager.getByDeviceId(dataDiskModel.getVmId());
            ResourceDetailDTO updateDTO = new ResourceDetailDTO();
            updateDTO.setId(ecsDetail.getId());
            String volumeId = Stream.of(ecsDetail.getShareVolumeId(), detail.getDeviceId())
                    .filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
            String dataDisk = Stream.of(ecsDetail.getShareDataDisk(), detail.getDataDisk())
                    .filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
            updateDTO.setShareVolumeId(volumeId);
            updateDTO.setShareDataDisk(dataDisk);
            resourceDetailManager.updateById(updateDTO);
        } else {
            detail.setMountOrNot("否");
        }

        detail.setCloudPlatform(dataDiskModel.getDomainCode());
        detail.setApplyTime(dataDiskModel.getApplyTime());
        detail.setResourcePoolId(dataDiskModel.getRegionId().toString());
        detail.setResourcePoolCode(dataDiskModel.getRegionCode());
        detail.setResourcePoolName(dataDiskModel.getRegionName());
        detail.setVmId(dataDiskModel.getVmId());
        detail.setEffectiveTime(LocalDateTime.now());
        detail.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), dataDiskModel.getApplyTime()));
    }


    public void convertEvs(ResourceDetailDTO detail, StandardWorkOrderProductDTO productDTO) {
        if (detail.getEffectiveTime() == null && detail.getCreateTime() != null) {
            detail.setEffectiveTime(detail.getCreateTime());
        }
        String propertySnapshot = productDTO.getPropertySnapshot();
        EvsModel dataDiskModel = JSON.parseObject(propertySnapshot, EvsModel.class);
        if (StringUtils.isNotEmpty(dataDiskModel.getVmId())) {
            //挂载成功时,detail才会传值过来的字段ecsName
            if (StringUtils.isEmpty(detail.getEcsName())) {
                detail.setMountOrNot("挂载执行中");
            } else {
                detail.setMountOrNot("是");
            }
            //如果是挂载了云主机，更新对应的云主机挂盘id和数据盘容量
            ResourceDetailDTO ecsDetail = resourceDetailManager.getByDeviceId(dataDiskModel.getVmId());
            String volumeId = Stream.of(ecsDetail.getVolumeId(), detail.getDeviceId())
                    .filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
            String dataDisk = Stream.of(ecsDetail.getDataDisk(), detail.getDataDisk())
                    .filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
            ecsDetail.setVolumeId(volumeId);
            ecsDetail.setDataDisk(dataDisk);
            resourceDetailManager.updateById(ecsDetail);
        } else {
            detail.setMountOrNot("否");
        }

        detail.setCloudPlatform(dataDiskModel.getDomainCode());
        detail.setApplyTime(dataDiskModel.getApplyTime());
        detail.setResourcePoolId(dataDiskModel.getRegionId().toString());
        detail.setResourcePoolCode(dataDiskModel.getRegionCode());
        detail.setResourcePoolName(dataDiskModel.getRegionName());
        detail.setVmId(dataDiskModel.getVmId());
        detail.setEffectiveTime(LocalDateTime.now());
        detail.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), dataDiskModel.getApplyTime()));
    }

    private void coverEip(ResourceDetailDTO detail, StandardWorkOrderProductDTO productDTO) {
        if (detail.getEffectiveTime() == null && detail.getCreateTime() != null) {
            detail.setEffectiveTime(detail.getCreateTime());
        }
        String propertySnapshot = productDTO.getPropertySnapshot();
        EipModel eipModel = JSON.parseObject(propertySnapshot, EipModel.class);
        if (StringUtils.isNotEmpty(eipModel.getVmId())) {
            // ipv6时，MC_IP_ADDRESS_T中的DEVICE_ID是null，统一调整成eipMode中的vmName
            detail.setRelatedDeviceName(eipModel.getVmName());
            detail.setRelatedDeviceId(eipModel.getVmId());
            detail.setRelatedDeviceType(ProductTypeEnum.ECS.getCode());
            //挂载成功时,detail才会传值过来的字段ecsName
            if (StringUtils.isEmpty(detail.getEcsName())) {
                detail.setMountOrNot("挂载执行中");
            } else {
                detail.setMountOrNot("是");
            }
            //如果是挂载了云主机，更新对应的eip和带宽
            resourceDetailManager.evsAddEipInfoByDeviceId(eipModel.getVmId(),
                    detail.getDeviceId(), detail.getEip(), detail.getBandWidth());
        } else {
            detail.setMountOrNot("否");
        }

        detail.setApplyTime(eipModel.getApplyTime());
        detail.setBandWidth(eipModel.getBandwidth().toString());
        detail.setResourcePoolId(eipModel.getRegionId().toString());
        detail.setResourcePoolName(eipModel.getRegionName());
        detail.setVmId(eipModel.getVmId());
        detail.setEffectiveTime(LocalDateTime.now());
        // 变更时候需要，先将eip的eipId设置成自己
        detail.setEipId(detail.getDeviceId());
        detail.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), eipModel.getApplyTime()));
//        detail.setEcsName(eipModel.getVmName());
        detail.setDeviceStatus("ACTIVE");
        detail.setBandWidth(bandWidth(detail.getBandWidth()));
        detail.setIpVersion(eipModel.getIpVersion());

//
//        //修改资源表
//        detail.setRelatedDeviceId(detail.getDeviceId());
////        detail.setRelatedDeviceType(detail.getDeviceType());
//        detail.setRelatedDeviceName(targetDTO.getDeviceName());
//        resourceDetailManager.updateById(resourceDetailDTO);
//        //更新target中的eipId
        ResourceDetailDTO targetResourceDetailDTO = new ResourceDetailDTO();
//        targetResourceDetailDTO.setId(targetDTO.getId());
//        targetResourceDetailDTO.setEipId(detailDTO.getDeviceId());
//        targetResourceDetailDTO.setEip(detailDTO.getEip());
//        targetResourceDetailDTO.setBandWidth(detailDTO.getBandWidth());
//        resourceDetailManager.updateById(targetResourceDetailDTO);
////        cmdbReportService.updateInstanceEcsByResource(resourceDetailManager.getById(targetResourceDetailDTO.getId()));

    }


    public void coverNat(ResourceDetailDTO detail, StandardWorkOrderProductDTO productDTO, List<ResourceDetailDTO> needInsertList) {
        String propertySnapshot = productDTO.getPropertySnapshot();
        NatGatwayModel natGatwayModel = JSON.parseObject(propertySnapshot, NatGatwayModel.class);
        detail.setSpec(natGatwayModel.getFlavorName());
        if (natGatwayModel.getBindPublicIp()) {
            detail.setBandWidth(StreamUtils.findAny(natGatwayModel.getEipModelList()).getBandwidth().toString());
        }
        detail.setVpcName(natGatwayModel.getPlaneNetworkModel().getName());
        detail.setVpcId(natGatwayModel.getPlaneNetworkModel().getId());
        PlaneNetworkModel planeNetworkModel = natGatwayModel.getPlaneNetworkModel();
        List<PlaneNetworkModel.Subnet> subnets = planeNetworkModel.getSubnets();
        detail.setSubnetName(StreamUtils.findAny(subnets).getSubnetName());
        detail.setSubnetId(StreamUtils.findAny(subnets).getSubnetId());
        detail.setEffectiveTime(LocalDateTime.now());
        detail.setApplyTime(natGatwayModel.getApplyTime());
        detail.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), natGatwayModel.getApplyTime()));
        detail.setResourcePoolId(natGatwayModel.getRegionId().toString());
        detail.setResourcePoolCode(natGatwayModel.getRegionCode());
        detail.setResourcePoolName(natGatwayModel.getRegionName());
        detail.setNetworkModelSnapshot(JSON.toJSONString(natGatwayModel.getPlaneNetworkModel()));
        detail.setBandWidth(bandWidth(detail.getBandWidth()));

        if (detail.getEipId() != null) {
            ResourceDetailDTO eipResourceDTO = converter.slb2Eip(detail);
            eipResourceDTO.setIpVersion(IPVersionEnum.IPV4.getDesc());
            needInsertList.add(eipResourceDTO);
        }
    }

    public void coverSlb(ResourceDetailDTO detail, StandardWorkOrderProductDTO productDTO, List<ResourceDetailDTO> needInsertList) {
        String propertySnapshot = productDTO.getPropertySnapshot();
        SlbModel slbModel = JSON.parseObject(propertySnapshot, SlbModel.class);
        detail.setSpec(slbModel.getFlavorName());
        if (slbModel.getBindPublicIp()) {
            detail.setBandWidth(StreamUtils.findAny(slbModel.getEipModelList()).getBandwidth().toString());
        }
        detail.setVpcName(slbModel.getPlaneNetworkModel().getName());
        detail.setVpcId(slbModel.getPlaneNetworkModel().getId());
        PlaneNetworkModel planeNetworkModel = slbModel.getPlaneNetworkModel();
        List<PlaneNetworkModel.Subnet> subnets = planeNetworkModel.getSubnets();
        detail.setSubnetName(StreamUtils.findAny(subnets).getSubnetName());
        detail.setSubnetId(StreamUtils.findAny(subnets).getSubnetId());
        detail.setEffectiveTime(LocalDateTime.now());
        detail.setApplyTime(slbModel.getApplyTime());
        detail.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), slbModel.getApplyTime()));
        detail.setResourcePoolId(slbModel.getRegionId().toString());
        detail.setResourcePoolCode(slbModel.getRegionCode());
        detail.setResourcePoolName(slbModel.getRegionName());
        detail.setNetworkModelSnapshot(JSON.toJSONString(slbModel.getPlaneNetworkModel()));
        detail.setBandWidth(bandWidth(detail.getBandWidth()));
        detail.setAzId(ObjNullUtils.isNotNull(slbModel.getAzId()) ? slbModel.getAzId().toString() : null);
        detail.setAzName(slbModel.getAzName());
        detail.setAzCode(slbModel.getAzCode());
        if (detail.getEipId() != null) {
            ResourceDetailDTO eipResourceDTO = converter.slb2Eip(detail);
            eipResourceDTO.setIpVersion(IPVersionEnum.IPV4.getDesc());
            needInsertList.add(eipResourceDTO);
        }
        Long azId = slbModel.getAzId();
        if (azId != null) {
            staticProductStockManager.decreaseStockByAzAndType(azId.toString(), "slb");
        } else {
            log.error("slb的azId为空,slbModel:{}", slbModel);
        }
    }

    public void coverObs(ResourceDetailDTO detail, StandardWorkOrderProductDTO productDTO) {
        String propertySnapshot = productDTO.getPropertySnapshot();
        ObsModel obsModel = JSON.parseObject(propertySnapshot, ObsModel.class);
        detail.setSpec(obsModel.getStorageDiskType() + " " + obsModel.getStorageDiskSize() + "GB");
        detail.setApplyTime(obsModel.getApplyTime());
        detail.setEffectiveTime(LocalDateTime.now());
        detail.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), obsModel.getApplyTime()));
    }

    public void convertVpn(ResourceDetailDTO detail, StandardWorkOrderProductDTO productDTO) {
        String propertySnapshot = productDTO.getPropertySnapshot();
        VpnModel vpnModel = JSON.parseObject(propertySnapshot, VpnModel.class);
        detail.setApplyTime(vpnModel.getApplyTime());
        detail.setEffectiveTime(LocalDateTime.now());
        detail.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), vpnModel.getApplyTime()));
    }

    public ResourceDetailDTO getResourceDetailDTO(ResourceDetailDTO detail, EcsModel escModel) {
        ResourceDetailDTO detailDTO = new ResourceDetailDTO();
        detailDTO.setOrderId(detail.getOrderId());
        detailDTO.setOrderCode(detail.getOrderCode());
        detailDTO.setTenantId(detail.getTenantId());
        detailDTO.setTenantName(detail.getTenantName());
        detailDTO.setBusinessSysName(detail.getBusinessSysName());
        detailDTO.setApplyUserId(detail.getApplyUserId());
        detailDTO.setApplyUserName(detail.getApplyUserName());
        detailDTO.setCreateTime(LocalDateTime.now());
        detailDTO.setStatus(1);
        detailDTO.setBillId(detail.getBillId());
        detailDTO.setCloudPlatform(detail.getDomainName());
        detailDTO.setBusinessSysId(detail.getBusinessSysId());
        detailDTO.setDomainCode(detail.getDomainCode());
        detailDTO.setDomainName(detail.getDomainName());
        detailDTO.setModuleId(detail.getModuleId());
        detailDTO.setModuleName(detail.getModuleName());
        detailDTO.setApplyTime(escModel.getApplyTime());
        detailDTO.setResourcePoolId(escModel.getRegionId().toString());
        detailDTO.setResourcePoolCode(escModel.getRegionCode());
        detailDTO.setResourcePoolName(escModel.getRegionName());
        detailDTO.setEffectiveTime(LocalDateTime.now());
        detailDTO.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), escModel.getApplyTime()));
        detailDTO.setResourceApplyTime(detail.getResourceApplyTime());
        detailDTO.setAzCode(detail.getAzCode());
        detailDTO.setAzName(detail.getAzName());
        detailDTO.setAzId(detail.getAzId());
        detailDTO.setEcsName(detail.getDeviceName() + "(" + detail.getIp() + ")");
        detailDTO.setMountOrNot("是");
        detailDTO.setVmId(detail.getDeviceId());
        return detailDTO;
    }

    public ResourceDetailDTO getResourceDetailDTO(ResourceDetailDTO detail, NpuPhysicalMachineModel npuModel) {
        ResourceDetailDTO detailDTO = new ResourceDetailDTO();
        detailDTO.setOrderId(detail.getOrderId());
        detailDTO.setOrderCode(detail.getOrderCode());
        detailDTO.setTenantId(detail.getTenantId());
        detailDTO.setTenantName(detail.getTenantName());
        detailDTO.setBusinessSysName(detail.getBusinessSysName());
        detailDTO.setApplyUserId(detail.getApplyUserId());
        detailDTO.setApplyUserName(detail.getApplyUserName());
        detailDTO.setCreateTime(LocalDateTime.now());
        detailDTO.setStatus(1);
        detailDTO.setBillId(detail.getBillId());
        detailDTO.setCloudPlatform(detail.getDomainName());
        detailDTO.setBusinessSysId(detail.getBusinessSysId());
        detailDTO.setDomainCode(detail.getDomainCode());
        detailDTO.setDomainName(detail.getDomainName());
        detailDTO.setModuleId(detail.getModuleId());
        detailDTO.setModuleName(detail.getModuleName());
        detailDTO.setApplyTime(npuModel.getApplyTime());
        detailDTO.setResourcePoolId(npuModel.getRegionId().toString());
        detailDTO.setResourcePoolCode(npuModel.getRegionCode());
        detailDTO.setResourcePoolName(npuModel.getRegionName());
        detailDTO.setEffectiveTime(LocalDateTime.now());
        detailDTO.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), npuModel.getApplyTime()));
        detailDTO.setResourceApplyTime(detail.getResourceApplyTime());
        detailDTO.setAzCode(detail.getAzCode());
        detailDTO.setAzName(detail.getAzName());
        detailDTO.setAzId(detail.getAzId());
        detailDTO.setEcsName(detail.getDeviceName() + "(" + detail.getIp() + ")");
        detailDTO.setMountOrNot("是");
        detailDTO.setVmId(detail.getDeviceId());
        return detailDTO;
    }


    private String bandWidth(String bandWidth) {
        if (ObjNullUtils.isNull(bandWidth)) {
            return null;
        }

        String trimmedBandWidth = bandWidth.trim();
        // 如果已经以Mbps或kbps结尾，则直接返回原值
        if (trimmedBandWidth.toLowerCase().endsWith("mbps")) {
            return trimmedBandWidth;
        }

        // 尝试解析为数字，如果是纯数字则添加单位
        return bandWidth + "Mbps";
    }

}
