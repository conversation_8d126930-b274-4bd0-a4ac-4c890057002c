package com.datatech.slgzt.model.resourcce;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * NPU物理机资源扩展信息
 * <AUTHOR>
 * @description NPU物理机资源概览显示
 * @date 2025年 01月15日
 */
@Data
@Accessors(chain = true)
public class ExtendNpuPhysicalMachineResource {

    /**
     * 资源开通数量
     */
    private Integer resourceNumbers = 0;

    /**
     * cpu大小
     */
    private String vcpuNumbers;

    /**
     * 内存大小
     */
    private String ramNumbers;

    /**
     * 存储大小
     */
    private String storageNumbers;

    private Integer npuNumbers;



    private transient Integer cpuNumbersTmp = 0;

    /**
     * 内存大小
     */
    private transient Integer ramNumbersTmp = 0;

    /**
     * 存储大小
     */
    private transient Integer storageNumbersTmp = 0;
}
