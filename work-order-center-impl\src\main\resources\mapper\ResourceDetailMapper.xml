<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datatech.slgzt.dao.mapper.ResourceDetailMapper">

    <resultMap id="BaseResultMap" type="com.datatech.slgzt.dao.model.ResourceDetailDO">
        <id property="goodsOrderId" column="GOODS_ORDER_ID" jdbcType="NUMERIC"/>
        <result property="orderId" column="ORDER_ID" jdbcType="NUMERIC"/>
        <result property="deviceId" column="DEVICE_ID" jdbcType="VARCHAR"/>
        <result property="deviceName" column="DEVICE_NAME" jdbcType="VARCHAR"/>
        <result property="resourceId" column="RESOURCE_ID" jdbcType="VARCHAR"/>
        <result property="osVersion" column="OS_VERSION" jdbcType="VARCHAR"/>
        <result property="spec" column="SPEC" jdbcType="VARCHAR"/>
        <result property="sysDisk" column="SYS_DISK" jdbcType="VARCHAR"/>
        <result property="dataDisk" column="DATA_DISK" jdbcType="VARCHAR"/>
        <result property="ip" column="IP" jdbcType="VARCHAR"/>
        <result property="eip" column="EIP" jdbcType="VARCHAR"/>
        <result property="bandWidth" column="BAND_WIDTH" jdbcType="VARCHAR"/>
        <result property="applyTime" column="APPLY_TIME" jdbcType="VARCHAR"/>
        <result property="tenantId" column="TENANT_ID" jdbcType="NUMERIC"/>
        <result property="tenantName" column="TENANT_NAME" jdbcType="VARCHAR"/>
        <result property="businessSysId" column="BUSINESS_SYS_ID" jdbcType="NUMERIC"/>
        <result property="businessSysName" column="BUSINESS_SYS_NAME" jdbcType="VARCHAR"/>
        <result property="cloudPlatform" column="CLOUD_PLATFORM" jdbcType="VARCHAR"/>
        <result property="resourcePoolId" column="RESOURCE_POOL_ID" jdbcType="NUMERIC"/>
        <result property="resourcePoolName" column="RESOURCE_POOL_NAME" jdbcType="VARCHAR"/>
        <result property="orderCode" column="ORDER_CODE" jdbcType="VARCHAR"/>
        <result property="resourceApplyTime" column="RESOURCE_APPLY_TIME" jdbcType="DATE"/>
        <result property="expireTime" column="EXPIRE_TIME" jdbcType="DATE"/>
        <result property="deviceStatus" column="DEVICE_STATUS" jdbcType="VARCHAR"/>
        <result property="applyUserId" column="APPLY_USER_ID" jdbcType="NUMERIC"/>
        <result property="applyUserName" column="APPLY_USER_NAME" jdbcType="VARCHAR"/>
        <result property="status" column="STATUS" jdbcType="NUMERIC"/>
        <result property="type" column="TYPE" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="DATE"/>
        <result property="effectiveTime" column="EFFECTIVE_TIME" jdbcType="DATE"/>
        <result property="vpcName" column="VPC_NAME" jdbcType="VARCHAR"/>
        <result property="subnetName" column="SUBNET_NAME" jdbcType="VARCHAR"/>
        <result property="vmId" column="VM_ID" jdbcType="VARCHAR"/>
        <result property="ecsName" column="ECS_NAME" jdbcType="VARCHAR"/>
        <result property="instanceUuid" column="INSTANCE_UUID" jdbcType="VARCHAR"/>
        <result property="netId" column="NET_ID" jdbcType="VARCHAR"/>
        <result property="netName" column="NET_NAME" jdbcType="VARCHAR"/>
        <result property="mac" column="MAC" jdbcType="VARCHAR"/>
        <result property="mountOrNot" column="MOUNT_OR_NOT" jdbcType="VARCHAR"/>
        <result property="recoveryStatus" column="RECOVERY_STATUS"/>
        <result property="disDimensionStatus" column="DIS_DIMENSION_STATUS" jdbcType="VARCHAR"/>
        <result property="manageIp" column="MANAGE_IP" jdbcType="VARCHAR"/>
        <result property="changeStatus" column="CHANGE_STATUS" jdbcType="VARCHAR"/>
        <result property="sourceType" column="SOURCE_TYPE" jdbcType="VARCHAR"/>
        <result property="moduleId" column="MODULE_ID" jdbcType="NUMERIC"/>
        <result property="moduleName" column="MODULE_NAME" jdbcType="VARCHAR"/>
        <result property="handoverStatus" column="HANDOVER_STATUS" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectByIdNoStatus" resultType="com.datatech.slgzt.dao.model.ResourceDetailDO">
        select *
        from
        WOC_RESOURCE_DETAIL
        where
        id = #{id}
    </select>


    <update id="updateConfigId">
        UPDATE WOC_RESOURCE_DETAIL
        SET
            CONFIG_ID = #{configId}
        <if test="manageIp != null and manageIp != ''">
            , MANAGE_IP = #{manageIp}
        </if>
        WHERE ID = #{id}
    </update>
    <select id="selectOrderGoodsByType" resultType="com.datatech.slgzt.dao.model.ResourceDetailDO">
        select
            GOODS_ORDER_ID,
            ORDER_ID,
            DEVICE_NAME,
            TYPE,
            SPEC,
            TO_CHAR(CREATE_TIME, 'MM') AS month
        from
            WOC_RESOURCE_DETAIL
        where
            STATUS = 1
            and CREATE_TIME &gt;= TRUNC(ADD_MONTHS(SYSDATE, -1), 'MM')
            and CREATE_TIME &lt; TRUNC(ADD_MONTHS(SYSDATE, 1), 'MM')
            <if test="recoveryType != null">
                and RECOVERY_STATUS != #{recoveryType}
            </if>
            <if test="businessIds != null and businessIds.size > 0">
                and BUSINESS_SYS_ID in
                <foreach collection="businessIds" item="businessId" open="(" separator="," close=")">
                    #{businessId}
                </foreach>
            </if>
            <if test="productTypes != null and productTypes.size > 0">
                and TYPE IN
                <foreach collection="productTypes" item="productType" open="(" separator="," close=")">
                    #{productType}
                </foreach>
            </if>
    </select>

    <select id="selectNetInfoByUuidAndIp" resultType="com.datatech.slgzt.model.dto.network.NetcardDetailDTO">
        SELECT INSTANCE_UUID, NET_ID, NET_NAME, MAC, IP FROM WOC_RESOURCE_DETAIL
        WHERE INSTANCE_UUID = #{instanceUuid} AND IP LIKE CONCAT(CONCAT('%',#{ip}),'%')
    </select>
    
    <select id="selectOperateVmResetPwd" resultType="com.datatech.slgzt.model.dto.ResetVmPwdDTO">
        select #{operate.newPwd} AS NEW_PASSWORD, c.CODE AS REGION_CODE, a.BILL_ID,
               LOWER(RAWTOHEX(SYS_GUID())) AS OPT_UUID, a.DEVICE_ID AS INSTANCE_ID from WOC_RESOURCE_DETAIL a
               LEFT JOIN MC_REGION_T c ON a.RESOURCE_POOL_ID = c.ID
        WHERE a.GOODS_ORDER_ID = #{operate.goodsOrderId}
    </select>

    <select id="selectOperateVm" resultType="com.datatech.slgzt.model.dto.OperateVmDTO">
        select #{operate.operationType} AS OPERATION_TYPE, c.CODE AS REGION_CODE, a.BILL_ID, c.TYPE AS SYSTEM_SOURCE,
               LOWER(RAWTOHEX(SYS_GUID())) AS OPT_UUID, a.DEVICE_ID AS INSTANCE_ID, a.DEVICE_STATUS, a.VOLUME_ID, a.EIP, a."TYPE" from WOC_RESOURCE_DETAIL a
               LEFT JOIN MC_REGION_T c ON a.RESOURCE_POOL_ID = c.ID
        WHERE
        1 = 1
        <if test="operate.id != null">
            and a.ID = #{operate.id}
        </if>
        <if test="operate.goodsOrderId != null">
            and a.GOODS_ORDER_ID = #{operate.goodsOrderId}
        </if>
    </select>
    <select id="selectRecoveryStatusByIds" resultType="com.datatech.slgzt.dao.model.ResourceDetailDO">
        select
            ID,
            DEVICE_STATUS,
            RECOVERY_STATUS
        from
            WOC_RESOURCE_DETAIL
        <where>
            <if test="status != null">
                and STATUS = #{status}
            </if>
            <if test="recoveryStatus != null">
                and (RECOVERY_STATUS is null
                or RECOVERY_STATUS = #{recoveryStatus})
            </if>
            <if test="ids != null and ids.size > 0">
                and ID in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <update id="updateDeviceStatusByDeviceId" parameterType="string">
        update WOC_RESOURCE_DETAIL set DEVICE_STATUS = #{deviceStatus} where DEVICE_ID = #{deviceId} and STATUS = 1
    </update>
    <update id="updateChangeStatusByIds">
        update WOC_RESOURCE_DETAIL set CHANGE_STATUS = #{changeStatus}
        where ID in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateChangeStatusByDeviceIds">
        update WOC_RESOURCE_DETAIL set CHANGE_STATUS = #{changeStatus}
        where DEVICE_ID in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectRegionCodeByOrderId" resultType="string">
        select r.CODE from  WOC_RESOURCE_DETAIL d
        left join MC_REGION_T r on d.RESOURCE_POOL_ID = r.ID
        where d.ORDER_ID = #{orderId}
    </select>
    <select id="selectNotUserModule" resultType="com.datatech.slgzt.model.dto.ModuleOfflineDTO">
        select * from (select d.DOMAIN_CODE,d.MODULE_ID,d.RESOURCE_POOL_ID as regionId,r.CODE as regionCode,sum(d.STATUS) as status
        from WOC_RESOURCE_DETAIL d
        left join MC_REGION_T r on d.RESOURCE_POOL_ID = r.ID
        where d.BUSINESS_SYS_ID = #{businessSysId}
        group by d.DOMAIN_CODE,d.MODULE_ID,d.RESOURCE_POOL_ID,r.CODE) t
        where t.status = 0
    </select>


    <update id="updateHandoverStatusByConfigId">
        UPDATE WOC_RESOURCE_DETAIL
        SET HANDOVER_STATUS = #{handoverStatus}
        WHERE CONFIG_ID = #{configId}
    </update>

    <select id="selectByExpireTime" resultType="com.datatech.slgzt.dao.model.ResourceDetailDO">
        select * from WOC_RESOURCE_DETAIL where STATUS = 1
        and EXPIRE_TIME BETWEEN TRUNC(SYSDATE) + INTERVAL '${time}' DAY AND TRUNC(SYSDATE) + INTERVAL '${endTime}' DAY
    </select>

    <select id="selectByExpireTimeThreeDay" resultType="com.datatech.slgzt.dao.model.ResourceDetailDO">
        select * from WOC_RESOURCE_DETAIL where STATUS = 1
        and TRUNC(EXPIRE_TIME) BETWEEN TRUNC(SYSDATE) - INTERVAL '3' DAY AND TRUNC(SYSDATE)- INTERVAL '1' DAY
    </select>

    <select id="selectExpireDetail" resultType="com.datatech.slgzt.dao.model.ResourceDetailDO">
        select * from WOC_RESOURCE_DETAIL where STATUS = 1
        and EXPIRE_TIME &lt; TRUNC(SYSDATE) - INTERVAL '3' DAY
    </select>

    <select id="selectResourceDetailOfObs" resultType="com.datatech.slgzt.model.dto.ResourceDetailDTO">
        select c.NAME as USERNAME,* from WOC_RESOURCE_DETAIL a left join MC_REGION_T b on a.RESOURCE_POOL_CODE = b.CODE
            inner join MC_PLATFORM_ACCOUNT_T c on b.ID = c.REGION_ID where a.STATUS = 1 and b.DELETED = 1
            and c.ACCOUNT_TYPE = 'ADMIN' and a.DOMAIN_CODE = 'plf_prov_moc_zj_h3c_obs' and a.type = 'obs'
        <if test="id != null">
            and a.ID = #{id}
        </if>
    </select>


</mapper>