package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.ModuleOfflineDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.network.NetcardDetailDTO;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.query.VmOperateQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

public interface ResourceDetailManager {


    ResourceDetailDTO selectByIdNoStatus(Long id);
    /**
     * saveResourceDetail
     */
    Long saveResourceDetail(ResourceDetailDTO dto);

    void updateById(ResourceDetailDTO dt);

    void updateVmIdAndEscNameById(ResourceDetailDTO dt);

    void updateVmIdAndEscNameById2(Long id, String vmId, String ecsName, Integer recoveryStatus, String mountOrNot);

    void updateShareVolumeInfoById(Long id, String shareVolumeId, String shareDataDisk);

    void updateEipById(ResourceDetailDTO dt);

    void updateSecurityGroupIdById(ResourceDetailDTO dt);

    void updateRecoveryTypeByIds(List<Long> ids, Integer status);

    void updateRecoveryTypeByDeviceIds(List<String> deviceIds, Integer status);

    void updateDisOrderStatusByDeviceIds(List<String> deviceIds, String status);

    void batchSaveResourceDetail(List<ResourceDetailDTO> dtos);

    List<ResourceDetailDTO> list(ResourceDetailQuery query);

    List<ResourceDetailDTO> listByDeviceIds(List<String> deviceIds);



    List<ResourceDetailDTO> listCorporate(ResourceDetailQuery query);

    void generateCorporateOrder(ResourceDetailQuery query);

    PageResult<ResourceDetailDTO> page(ResourceDetailQuery query);

    PageResult<ResourceDetailDTO> pageEX(ResourceDetailQuery query);


    ResourceDetailDTO getByGoodsOrderId(Long id);
    List<ResourceDetailDTO> selectOrderGoodsByType(List<String> businessIds, List<String> productTypes, Integer recoveryType);

    ResourceDetailDTO getByDeviceId(String deviceId);

    void updateConfigId(String configId, Long id, String manageIp);

    NetcardDetailDTO selectNetInfoByUuidAndIp(String instanceUuid, String ip);

    CommonResult<String> operateVm(VmOperateQuery operate);

    CommonResult operateNPM(VmOperateQuery operate);

    List<Long> selectBusinessIdByGoodsIds(List<String> goodsIds);

    List<ResourceDetailDTO> selectByGoodsIds(List<String> goodsIdList);

    ResourceDetailDTO getById(Long id);

    void deleteById(Long id);

    List<ResourceDetailDTO> selectRecoveryStatusByIds(List<String> ids, Integer recoveryStatus, Integer status);

    String selectByBusinessSystemId(Long businessSystemId);

    List<String> selectRegionCodeByOrderId(String orderId);

    List<ModuleOfflineDTO> selectNotUserModule(Long businessSysId);

    ResourceDetailDTO getConfigId(String configId);

    void updateChangeStatusByIds(List<Long> resourceIds, String changeStatus);

    void updateChangeStatusByDeviceIds(List<String> deviceIds, String changeStatus);

    void updateEipInfoByEipId(String eipId, String eip, String bandWidth);

    void evsAddEipInfoByDeviceId(String deviceId, String eipId, String eip, String bandWidth);

    void clearEipInfoByEipId(String eipId);

    void clearBackupInfoById(Long id);

    void clearEipInfoById(Long id, String eip);

    void clearRelatedInfoById(Long id);

    void updateEvsInfoById(ResourceDetailDTO dto);

    void updateHandoverStatusByConfigId(String configId, String handoverStatus);

    List<ResourceDetailDTO> selectByExpireTime(String time, String endTime);
    List<ResourceDetailDTO> selectByExpireTimeThreeDay();
    List<ResourceDetailDTO> selectExpireDetail();

    List<ResourceDetailDTO> selectResourceDetailOfObs(Long id);
}
