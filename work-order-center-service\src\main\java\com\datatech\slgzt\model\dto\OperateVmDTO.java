package com.datatech.slgzt.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: taskcenterproject
 * @description: 操作云主机实体类
 * @author: LK
 * @create: 2025-02-18 14:50
 **/
@Data
public class OperateVmDTO implements Serializable {

    /**
     * 区域编码
     */
    private String regionCode;

    /**
     * 计费号
     */
    private String billId;

    /**
     * 来源系统
     */
    private String systemSource;

    /**
     * 操作编号
     */
    private String optUuid;
    /**
     * 云主机id（VM_ID）
     */
    private String instanceId;

    /**
     * 云主机操作类型：START，STOP，RESTART
     */
    private String operationType;

    /**
     * 资源状态
     */
    private String deviceStatus;

    /**
     * 镜像id
     */
    private String imageId;

    /**
     * 数据盘id
     */
    private String volumeId;

    /**
     * eip
     */
    private String eip;

    private String type;

}
