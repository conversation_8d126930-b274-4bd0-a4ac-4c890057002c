package com.datatech.slgzt.model.dto.xieyun;

import lombok.Data;

@Data
public class QuotaResultDTO {
    /**
     * CPU(核)
     */
    private QuotaDetailResultDTO cpu;
    /**
     * 内存(Gi)
     */
    private QuotaDetailResultDTO memory;
    /**
     * 物理GPU卡数(卡)
     */
    private QuotaDetailResultDTO gpuCore;
    /**
     * 虚拟GPU卡数(卡)
     */
    private QuotaDetailResultDTO gpuVirtualCore;
    /**
     * GPU算力
     */
    private QuotaDetailResultDTO gpuRatio;
    /**
     * 虚拟GPU显存(Gi)
     */
    private QuotaDetailResultDTO gpuVirtualMemory;
    
    private QuotaDetailResultDTO gpuMemory;
    private QuotaDetailResultDTO physicalGpu;
    private QuotaDetailResultDTO npu;
}


