package com.datatech.slgzt.model.vo.resource;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import lombok.Data;

import java.util.Date;

/**
 * NPU裸金属导出VO
 * <AUTHOR>
 * @description NPU裸金属资源导出实体类
 * @date 2025年 01月16日
 */
@Data
public class NpuPhysicalMachineExportVO {

    @ExcelExportHeader(value = "设备名称")
    private String deviceName;

    @ExcelExportHeader(value = "资源id")
    private String deviceId;

    @ExcelExportHeader(value = "系统版本")
    private String osVersion;

    @ExcelExportHeader(value = "实例规格")
    private String spec;

    @ExcelExportHeader(value = "IP")
    private String ip;

//    @ExcelExportHeader(value = "弹性公网IP")
//    private String eip;
//
//    @ExcelExportHeader(value = "带宽")
//    private String bandWidth;

    @ExcelExportHeader(value = "申请时长")
    private String applyTime;

    @ExcelExportHeader(value = "租户名称")
    private String tenantName;

    @ExcelExportHeader(value = "业务系统")
    private String businessSysName;

    @ExcelExportHeader(value = "所属云")
    private String cloudPlatform;

    @ExcelExportHeader(value = "资源池")
    private String resourcePoolName;

    @ExcelExportHeader(value = "工单编号")
    private String orderCode;

    @ExcelExportHeader(value = "开通时间")
    private Date resourceApplyTime;

    @ExcelExportHeader(value = "到期时间")
    private Date expireTime;

    @ExcelExportHeader(value = "状态")
    private String deviceStatus;

    @ExcelExportHeader(value = "申请人")
    private String applyUserName;

    @ExcelExportHeader(value = "计费号")
    private String billId;

    @ExcelExportHeader(value = "UUID")
    private String instanceUuid;
}
