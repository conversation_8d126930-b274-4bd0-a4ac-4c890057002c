# NPU容器配额底层资源分配实现

## 修改概述

本次修改实现了NPU容器配额的底层资源分配逻辑，支持在协云环境创建Job中处理NPU资源的组织级和项目级配额分配。

## 修改的文件

### 1. 数据模型层修改

#### ContainerQuotaDO.java
- 新增 `npu` 字段：NPU资源数量
- 新增 `type` 字段：容器类型（0-普通容器，1-NPU容器）

#### ContainerQuotaDTO.java  
- 新增 `npu` 字段：NPU资源数量
- 新增 `containerType` 字段：容器类型（0-普通容器，1-NPU容器）

#### CQModel.java
- 新增 `npu` 字段：NPU资源数量（非必填）
- 新增 `containerType` 字段：容器类型（非必填，默认为0）

### 2. 服务层修改

#### XieyunEnvironmentServiceImpl.java
- 在Job参数中新增 `npuValue` 和 `containerType` 参数传递
- 支持从CQModel中获取NPU相关参数并传递给Job

#### XieyunEnvironmentCreateJobDef.java
- **orgQuotaStep()方法修改**：
  - 新增NPU参数获取和转换逻辑
  - 新增NPU资源校验（仅当容器类型为NPU容器时）
  - 根据容器类型分配不同的资源：
    - NPU容器（type=1）：使用NPU字段，GPU相关字段设为0
    - 普通容器（type=0）：使用GPU相关字段，NPU字段设为0

- **projectQuotaStep()方法修改**：
  - 新增NPU参数获取和转换逻辑
  - 根据容器类型分配不同的资源：
    - NPU容器（type=1）：使用NPU字段，GPU相关字段设为0
    - 普通容器（type=0）：使用GPU相关字段，NPU字段设为0

## 业务逻辑

### 字段区分逻辑
- **TYPE=0（普通容器配额）**：使用V_CPUS、RAM、GPU_RATIO、GPU_VIRTUAL_MEMORY、GPU_CORE、GPU_VIRTUAL_CORE字段，NPU字段为0
- **TYPE=1（NPU容器配额）**：使用V_CPUS、RAM、NPU字段，其他GPU相关字段为0

### 资源分配流程
1. 从Job参数中获取容器类型和NPU资源参数
2. 根据容器类型进行资源校验（NPU容器需校验NPU资源）
3. 根据容器类型分配相应的资源配额
4. 调用底层接口进行实际的资源分配

## 向后兼容性
- 所有新增字段都有默认值，不影响现有普通容器配额功能
- 现有的GPU相关逻辑保持不变
- 当containerType为0或null时，按照原有逻辑处理

## 技术要点
- 使用BigDecimal进行精确的数值计算
- 通过容器类型字段区分不同的资源分配策略
- 复用现有的底层资源分配接口（XieyunOrgQuotaOpm和XieyunProjectQuotaOpm已支持npuValue字段）
- 保持现有代码架构和风格一致

## 后续工作
本次实现仅完成底层资源分配逻辑，后续还需要：
1. XieyunEnvironmentCreateJobListener中NPU容器配额数据的插入逻辑
2. NPU容器配额的标准工单开通逻辑
