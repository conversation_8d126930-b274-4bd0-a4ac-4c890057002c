package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.AzMapper;
import com.datatech.slgzt.dao.model.AzDO;
import com.datatech.slgzt.model.query.AzQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月10日 14:20:56
 */
@Repository
public class AzDAO {

    @Resource
    private AzMapper azMapper;


    /**
     * list
     */
    public List<AzDO> list(AzQuery query) {
        return azMapper.selectList(Wrappers.<AzDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getRegionId()), AzDO::getRegionId, query.getRegionId())
                .eq(ObjNullUtils.isNotNull(query.getCode()), AzDO::getCode, query.getCode())
                .eq(AzDO::getDeleted, 1)
                .in(ObjNullUtils.isNotNull(query.getRegionIds()), AzDO::getRegionId, query.getRegionIds())
        );
    }

    /**
     * list
     */
    public AzDO getByCode(String code) {
        return azMapper.selectOne(Wrappers.<AzDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(code), AzDO::getCode, code)
        );
    }

}
